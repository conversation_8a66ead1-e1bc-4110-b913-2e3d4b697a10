'use client'

import { useEffect, useState } from 'react'
import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { SetScreenErrorState } from '@/components/workout/SetScreenErrorState'
import {
  ExercisePageErrorBoundary,
  WorkoutErrorBoundary,
} from '@/components/workout/ExercisePageErrorBoundary'
import { ExerciseCompleteViewV2 } from './ExerciseCompleteViewV2'
import { WorkoutCompleteView } from '@/components/workout/WorkoutCompleteView'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'

interface ExercisePageStatesProps {
  loadingError: Error | string | null
  workoutError: Error | null
  retryInitialization: () => Promise<void>
  isInitializing: boolean
  isLoadingWorkout: boolean
  isLoadingRecommendation: boolean
  isLoading: boolean
  recommendation: RecommendationModel | null
  currentExercise: ExerciseModel | null
  workoutSession: unknown
  error: string | null
  refetchRecommendation: () => Promise<void>
  showComplete: boolean
  showExerciseComplete: boolean
  currentSet: WorkoutLogSerieModel | null
  isLastExercise: boolean
  handleSaveSet: () => void
}

export function ExercisePageStates({
  loadingError,
  workoutError,
  retryInitialization,
  isInitializing,
  isLoadingWorkout,
  isLoadingRecommendation,
  isLoading,
  recommendation,
  currentExercise,
  workoutSession,
  error,
  refetchRecommendation,
  showComplete,
  showExerciseComplete,
  currentSet,
  isLastExercise,
  handleSaveSet,
}: ExercisePageStatesProps) {
  // Timeout fallback to prevent infinite loading states
  const [hasTimedOut, setHasTimedOut] = useState(false)

  useEffect(() => {
    // Reset timeout when loading states change
    setHasTimedOut(false)

    // Set timeout for recommendation loading to prevent infinite skeletons
    if (isLoadingRecommendation && !recommendation) {
      const timeoutId = setTimeout(() => {
        setHasTimedOut(true)
      }, 45000) // 45 seconds timeout (longer than coordinator timeout)

      return () => clearTimeout(timeoutId)
    }

    // Return undefined when no cleanup is needed
    return undefined
  }, [isLoadingRecommendation, recommendation])
  // Show error state with retry option
  if (loadingError) {
    const errorObj =
      typeof loadingError === 'string' ? new Error(loadingError) : loadingError
    return (
      <ExercisePageErrorBoundary
        error={errorObj}
        onRetry={retryInitialization}
      />
    )
  }

  // Handle workout errors
  if (workoutError) {
    return <WorkoutErrorBoundary error={workoutError} />
  }

  // Show loading only when recommendation is not loaded and hasn't timed out
  if ((!recommendation || isLoadingRecommendation) && !hasTimedOut) {
    return (
      <SetScreenLoadingState
        exerciseName={currentExercise?.Label}
        showDetailedSkeleton
        isLoadingRecommendations={isLoadingRecommendation}
      />
    )
  }

  // Handle timeout case - show error state with retry option
  if (hasTimedOut && !recommendation) {
    return (
      <SetScreenErrorState
        onRetry={() => {
          setHasTimedOut(false)
          refetchRecommendation()
        }}
      />
    )
  }

  // For other loading states, check if we have minimum required data to proceed
  if (
    (isInitializing || isLoadingWorkout || isLoading) &&
    (!currentExercise || !workoutSession)
  ) {
    return (
      <SetScreenLoadingState
        exerciseName={currentExercise?.Label}
        showDetailedSkeleton={false}
        isLoadingRecommendations={false}
      />
    )
  }

  // Error state
  if (error) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Workout complete
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  // Exercise complete - only show if we have loaded data and exercise is truly complete
  if (showExerciseComplete || (recommendation && !currentSet)) {
    return (
      <ExerciseCompleteViewV2
        exercise={currentExercise}
        isLastExercise={isLastExercise}
        onContinue={handleSaveSet}
      />
    )
  }

  return null
}
